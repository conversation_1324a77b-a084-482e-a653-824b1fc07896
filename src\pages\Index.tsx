import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Sprout, Users, Smartphone, Truck, ArrowRight, Leaf, ShoppingCart, MapPin, Play, Menu, X, Sparkles } from 'lucide-react';

const Index = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [scrollY, setScrollY] = useState(0);
  const [isFloatingNav, setIsFloatingNav] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);
      // Show floating nav when scrolled past hero section (approximately 80vh)
      setIsFloatingNav(currentScrollY > window.innerHeight * 0.8);
      // Close mobile menu when scrolling
      if (isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMobileMenuOpen]);

  const businessPillars = [
    {
      icon: <Sprout className="w-8 h-8" />,
      title: "Direct from Farmers",
      subtitle: "Ethical Procurement",
      description: "We work directly with local farmers to source natural, chemical-free vegetables, ensuring fair compensation and eliminating middlemen.",
      stats: "Higher-than-market prices for farmers",
      color: "from-primary-400 to-primary-600"
    },
    {
      icon: <ShoppingCart className="w-8 h-8" />,
      title: "B2B Partner Network",
      subtitle: "Empowering Local Stores",
      description: "Medium-sized grocery stores get access to premium vegetables and digital listing on our platform to compete with major delivery apps.",
      stats: "Wholesale rates + Digital expansion",
      color: "from-secondary-400 to-secondary-600"
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Tech Platform",
      subtitle: "3-Way Integration",
      description: "Customer app, business partner web app, and delivery partner app create a seamless ecosystem for all stakeholders.",
      stats: "Real-time management & tracking",
      color: "from-accent-400 to-accent-600"
    },
    {
      icon: <Truck className="w-8 h-8" />,
      title: "Delivery Network",
      subtitle: "Local Youth Powered",
      description: "We train and onboard local youngsters as delivery partners, providing consistent earning opportunities and career growth.",
      stats: "Hyperlocal & fast delivery",
      color: "from-primary-500 to-secondary-500"
    }
  ];

  const impacts = [
    { label: "Farmer Income", value: "+40%", description: "Above market rates" },
    { label: "Delivery Time", value: "30min", description: "Average delivery" },
    { label: "Youth Jobs", value: "500+", description: "Employment created" },
    { label: "Partner Stores", value: "50+", description: "In Panipat" }
  ];

  return (
    <div className="min-h-screen">
      {/* Floating Navigation Bar */}
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ease-out ${
          isFloatingNav
            ? 'translate-y-0 opacity-100'
            : '-translate-y-full opacity-0'
        }`}
      >
        <div className="mx-4 mt-4">
          <div className={`bg-white/95 backdrop-blur-xl rounded-2xl border border-white/20 px-6 py-3 transition-all duration-500 ${
              isFloatingNav
                ? 'shadow-lg shadow-black/10 scale-100'
                : 'shadow-none scale-95'
            }`}>
            <div className="flex items-center justify-between max-w-7xl mx-auto">
              {/* Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-sm">
                  <Leaf className="w-5 h-5 text-white" />
                </div>
                <span className="text-lg font-bold text-gray-800">Foodhub Trading</span>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-1">
                {['Solutions', 'Partnership', 'Pricing', 'About', 'Contact'].map((item, index) => (
                  <Button
                    key={item}
                    variant="ghost"
                    size="sm"
                    className="rounded-full px-4 py-2 text-sm text-gray-600 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200"
                  >
                    {item}
                  </Button>
                ))}
                <div className="flex items-center space-x-2 ml-4">
                  <Badge variant="secondary" className="bg-primary-100 text-primary-700 text-xs px-2 py-1">
                    New
                  </Badge>
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white rounded-full px-4 py-2 text-sm shadow-sm hover:shadow-md transition-all duration-200"
                  >
                    <Sparkles className="w-3 h-3 mr-1" />
                    Get Started
                  </Button>
                </div>
              </div>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden p-2"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5" />
                ) : (
                  <Menu className="w-5 h-5" />
                )}
              </Button>
            </div>

            {/* Mobile Menu */}
            {isMobileMenuOpen && (
              <div className="md:hidden mt-4 pt-4 border-t border-gray-100 animate-fade-in">
                <div className="flex flex-col space-y-2">
                  {['Solutions', 'Partnership', 'Pricing', 'About', 'Contact'].map((item) => (
                    <Button
                      key={item}
                      variant="ghost"
                      className="justify-start text-gray-600 hover:bg-primary-50 hover:text-primary-700"
                    >
                      {item}
                    </Button>
                  ))}
                  <div className="flex items-center justify-between pt-2">
                    <Badge variant="secondary" className="bg-primary-100 text-primary-700">
                      New
                    </Badge>
                    <Button size="sm" className="bg-gradient-to-r from-primary-600 to-secondary-600 text-white">
                      <Sparkles className="w-3 h-3 mr-1" />
                      Get Started
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </nav>
      {/* Hero Section with Modern UI */}
      <section className="relative min-h-screen overflow-hidden">
        {/* Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1615729947596-a598e5de0ab3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`
          }}
        >
          {/* Overlay for better contrast */}
          <div className="absolute inset-0 bg-black/20"></div>
        </div>

        {/* Main Content Container */}
        <div className="relative z-10 min-h-screen flex items-center justify-center p-4 md:p-8">
          <div className="w-full max-w-7xl mx-auto">
            
            {/* White Rounded Container */}
            <div className="bg-white rounded-3xl md:rounded-[3rem] shadow-2xl overflow-hidden">
              
              {/* Header Navigation */}
              <div className="px-6 md:px-12 pt-6 md:pt-8 pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                      <Leaf className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl md:text-2xl font-bold text-gray-800">Foodhub Trading</span>
                  </div>
                  
                  {/* Navigation Pills */}
                  <div className="hidden md:flex items-center space-x-2">
                    <Button variant="ghost" className="rounded-full px-6 py-2 text-gray-600 hover:bg-gray-100">
                      Solutions
                    </Button>
                    <Button variant="ghost" className="rounded-full px-6 py-2 text-gray-600 hover:bg-gray-100">
                      Partnership
                    </Button>
                    <Button variant="ghost" className="rounded-full px-6 py-2 text-gray-600 hover:bg-gray-100">
                      Pricing
                    </Button>
                    <Button variant="ghost" className="rounded-full px-6 py-2 text-gray-600 hover:bg-gray-100">
                      Contact
                    </Button>
                    <Button className="rounded-full bg-gray-800 hover:bg-gray-900 text-white px-6 py-2 ml-4">
                      • LET'S TALK!
                    </Button>
                  </div>
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 px-6 md:px-12 pb-8 md:pb-12">
                
                {/* Left Content */}
                <div className="space-y-8">
                  <div>
                    <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight mb-6">
                      Unleash your 
                      <span className="relative inline-block ml-4">
                        <div className="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl inline-block mr-2 align-middle"></div>
                        creativity
                      </span>
                      <br />
                      with our platform
                    </h1>
                    
                    <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-lg">
                      Revolutionizing how fresh vegetables move from farmers to retailers and consumers through ethical procurement, 
                      technology, and hyperlocal delivery.
                    </p>
                  </div>

                  <Button className="bg-primary-500 hover:bg-primary-600 text-white rounded-full px-8 py-4 text-lg font-semibold">
                    GET STARTED
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>

                  {/* Bottom Card */}
                  <div className="bg-gray-50 rounded-2xl p-6 flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-xl flex items-center justify-center">
                      <Play className="w-6 h-6 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        How Foodhub is changing agriculture:
                      </h3>
                      <p className="text-gray-600 text-sm mb-2">
                        Farmer-first supply chain of the future
                      </p>
                      <p className="text-xs text-gray-500">• 8 minutes</p>
                    </div>
                  </div>
                </div>

                {/* Right Side - Interactive Elements */}
                <div className="relative">
                  {/* Main Visual Element */}
                  <div className="relative">
                    {/* Background Image */}
                    <div 
                      className="w-full h-80 md:h-96 bg-cover bg-center rounded-3xl relative overflow-hidden"
                      style={{
                        backgroundImage: `url('https://images.unsplash.com/photo-1518495973542-4542c06a5843?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')`
                      }}
                    >
                      {/* Overlay */}
                      <div className="absolute inset-0 bg-primary-500/20"></div>
                      
                      {/* Stats Cards */}
                      <div className="absolute top-6 right-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg">
                        <p className="text-sm text-gray-600 mb-1">Reach of farmers using</p>
                        <p className="text-sm text-gray-600 mb-2">the platform now</p>
                        <div className="flex items-center">
                          <span className="text-3xl font-bold text-primary-600">+200</span>
                          <span className="text-lg text-primary-600 ml-1">%</span>
                          <ArrowRight className="w-4 h-4 ml-2 text-gray-400" />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">vs last month</p>
                      </div>

                      <div className="absolute bottom-6 left-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg">
                        <p className="text-sm text-gray-600 mb-1">Results of monetization of</p>
                        <p className="text-sm text-gray-600 mb-2">our users' content</p>
                        <div className="flex items-center">
                          <span className="text-3xl font-bold text-primary-600">72,5</span>
                          <span className="text-lg text-primary-600 ml-1">%</span>
                          <ArrowRight className="w-4 h-4 ml-2 text-gray-400" />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">more than average</p>
                      </div>

                      {/* User Avatars */}
                      <div className="absolute bottom-6 right-6 flex -space-x-2">
                        <div className="w-8 h-8 bg-primary-200 rounded-full border-2 border-white"></div>
                        <div className="w-8 h-8 bg-secondary-200 rounded-full border-2 border-white"></div>
                        <div className="w-8 h-8 bg-accent-200 rounded-full border-2 border-white"></div>
                        <div className="w-8 h-8 bg-primary-300 rounded-full border-2 border-white"></div>
                        <div className="w-8 h-8 bg-secondary-300 rounded-full border-2 border-white"></div>
                        <div className="w-8 h-8 bg-accent-300 rounded-full border-2 border-white"></div>
                      </div>

                      {/* Bottom Right Cards */}
                      <div className="absolute -bottom-4 -right-4 flex space-x-2">
                        <div className="w-12 h-16 bg-white rounded-xl shadow-lg"></div>
                        <div className="w-12 h-20 bg-white rounded-xl shadow-lg"></div>
                        <div className="w-12 h-14 bg-white rounded-xl shadow-lg"></div>
                        <div className="w-12 h-18 bg-white rounded-xl shadow-lg"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {impacts.map((impact, index) => (
              <div key={index} className="text-center group">
                <div className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                  <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">{impact.value}</div>
                  <div className="text-gray-800 font-semibold mb-1">{impact.label}</div>
                  <div className="text-sm text-gray-500">{impact.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Business Model Section */}
      <section id="model" className="py-20 px-4 bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Our <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">4-Pillar</span> Business Model
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A comprehensive ecosystem that connects farmers, retailers, technology, and delivery partners for sustainable growth.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {businessPillars.map((pillar, index) => (
              <Card 
                key={index}
                className={`group cursor-pointer transition-all duration-500 hover:shadow-2xl ${
                  hoveredCard === index ? 'scale-105' : ''
                }`}
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                <CardContent className="p-8">
                  <div className={`w-16 h-16 bg-gradient-to-br ${pillar.color} rounded-2xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    {pillar.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">{pillar.title}</h3>
                  <p className="text-primary-600 font-semibold mb-4">{pillar.subtitle}</p>
                  <p className="text-gray-600 mb-4 leading-relaxed">{pillar.description}</p>
                  <Badge variant="secondary" className="bg-primary-100 text-primary-700">
                    {pillar.stats}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* What Sets Us Apart */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">What Sets Us Apart</h2>
            <p className="text-xl text-gray-600">Creating value for every stakeholder in the supply chain</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { title: "Farmer Impact", value: "Higher income, consistent demand, no middlemen", icon: <Sprout className="w-6 h-6" /> },
              { title: "Retailer Benefit", value: "Hassle-free sourcing, digital listing, better margins", icon: <ShoppingCart className="w-6 h-6" /> },
              { title: "Customer Value", value: "Fresh, quality produce at competitive prices", icon: <Users className="w-6 h-6" /> },
              { title: "Youth Employment", value: "Flexible income through delivery roles", icon: <Truck className="w-6 h-6" /> },
              { title: "Technology", value: "All-in-one platform for procurement, sales, fulfillment", icon: <Smartphone className="w-6 h-6" /> },
              { title: "Social Mission", value: "Solving real problems across the value chain", icon: <Leaf className="w-6 h-6" /> }
            ].map((item, index) => (
              <div key={index} className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 group">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                  {item.icon}
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.value}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Launch Location */}
      <section className="py-20 px-4 bg-gradient-to-br from-secondary-50 to-accent-50">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <Badge className="mb-6 bg-secondary-100 text-secondary-700 hover:bg-secondary-200 px-4 py-2">
              <MapPin className="w-4 h-4 mr-2" />
              Pilot City
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Starting in Panipat</h2>
            <p className="text-xl text-gray-600 mb-8">
              We're launching in Panipat, a growing urban hub, to pilot and perfect our model. 
              Once optimized, the same structure is scalable to other Tier 2 & Tier 3 cities across India.
            </p>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Our Vision</h3>
              <blockquote className="text-xl text-gray-600 italic">
                "To create a tech-enabled, farmer-first food supply chain that empowers every link — from soil to doorstep."
              </blockquote>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-primary-500 to-secondary-500">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto text-white">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Transform Agriculture?</h2>
            <p className="text-xl mb-8 opacity-90">
              Join us in creating a sustainable, technology-driven food supply chain that benefits everyone.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button size="lg" variant="secondary" className="bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 text-lg">
                Partner with Us
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-4 text-lg">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-gray-900 text-white">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                <Leaf className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold">Foodhub Trading</span>
            </div>
            <div className="text-gray-400 text-center md:text-right">
              <p>&copy; 2024 Foodhub Trading. Revolutionizing agriculture, one delivery at a time.</p>
              <p className="mt-2">📍 Panipat, Haryana | 🌱 Farm to Table Technology</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
